export default {
  'intention.management.intent.classification': 'Category: ',
  'intention.management.intent.classification.placeholder':
    'Please select the intent category',
  'intention.management.intent.language': 'Language: ',
  'intention.management.intent.language.placeholder':
    'Please select the intend language',
  'intention.management.intent.name': 'Intent name: ',
  'intention.management.intent.name.placeholder':
    'Please enter the intent name',
  'intention.management.create.intention.text': 'Create a new intent',
  'intention.management.user.number': '{num} times',
  'intention.management.user.number.month.tips':
    'Trigger times in the current month',
  'intention.management.intent.request.reason': 'Intent request reason: ',
  'intention.management.create.information': 'Creation information: ',
  'intention.management.create.intelligent.agent': 'Create AI Agent',
  'intention.management.intelligent.agent': 'AI Agent',
  'intention.management.delete.tips':
    'Are you sure you want to delete this content?',

  // 添加意图
  'new.intent.title.add': 'Add intent',
  'new.intent.title.edit': 'Edit intent',
  'new.intent.intention.basic.information': 'Intent basic information',
  'new.intent.intention.name': 'Intent name',
  'new.intent.intention.name.maxlength': 'Length cannot exceed 80 characters',
  'new.intent.intention.language': 'Intent language',
  'new.intent.intention.classification': 'Intent category',
  'new.intent.intention.request.reason': 'Intent request reason',
  'new.intent.intention.request.reason.placeholder':
    'Please enter the reason for triggering this intent by the customer, e.g., customer requests to check order status',
  'new.intent.intention.reason.maxlength':
    'Length cannot exceed 2000 characters',
  'new.intent.intention.basic.information.tips':
    'Intent name and intent request reason are important factors for AIGC to identify customer intent.',
  'new.intent.intention. language.information': 'Intent utterance',
  'new.intent.static.language.technique': 'Static utterance',
  'new.intent.dynamic.speech.technique': 'Dynamic utterance',
  'new.intent.static.language.technique.placeholder':
    'Please enter static intent utterance',
  'new.intent.dynamic.speech.technique.placeholder':
    'Please enter dynamic utterance',
  'new.intent.dynamic.speech.technique.placeholder2':
    'Please enter dynamic intent utterance',
  'new.intent.ai.static.language.technique': 'AI generate intent utterance',
  'new.intent.add.static.language.technique': 'Add intent utterance',
  'new.intent.intention.dynamic.speech.technique.tips':
    "Dynamic utterance can include dynamic attributes, which are formatted as {Device}, where 'Device' is the attribute name and only supports English.",
  'new.ai.generate.intent.intention.language':
    'Select intent utterance language',
  'new.intent.intention.language.attribute': 'Intent utterance attributes',
  'new.intent.intention.language.attribute.name': 'Attribute name',
  'new.intent.intention.language.attribute.name.placeholder':
    'Please enter the attribute name',
  'new.intent.intention.language.attribute.example': 'Attribute value example',
  'new.intent.intention.language.attribute.example.placeholder':
    'Please enter the attribute value',
  'new.intent.intention.language.attribute.example.tips':
    'The attribute value example is the key reference for extracting the current attribute. For example, if the attribute is Device, the example could be air conditioner, dehumidifier, etc.',
  'new.intent.intention.attribute.description': 'Attribute description',
  'new.intent.intention.attribute.description.placeholder':
    'Please enter the description of the variable',
  'new.intent.intention.attribute.mandatory': "It's a required Attribute",
  'new.intent.intention.attribute.verify.format':
    "Select the attribute's validation format",
  'new.intent.intention.attribute.verify.format.placeholder':
    "Please select the attribute's validation format",
  'new.intent.intention.attribute.verify.rule.placeholder':
    "Please enter the attribute's validation regex",
  'new.intent.intention.attribute.verify.rule.maxlength':
    'Length cannot exceed 200 characters',
  'new.intent.intention.failed.verification.attempts':
    'Verification failure retry attempts',
  'new.intent.intention.failed.verification.attempts.placeholder':
    'Please enter the number of verification attempts',
  'new.intent.intention.failed.num.verification.attempts.placeholder':
    'The maximum number of attempts cannot exceed 99',
  'new.intent.intention.reply.after.verification.failure':
    'Reply after verification failure',
  'new.intent.intention.reply.after.verification.failure.placeholder':
    'Please enter the reply after verification failure',
  'new.intent.intention.reply.after.verification.failure.final':
    'Final reply after verification failure',
  'new.intent.intention.reply.after.verification.failure.final.placeholder':
    'Please enter the final reply after verification failure',
  'new.intent.intelligent.agent.variables': 'Variables stored in the AI Agent',
  'new.intent.intelligent.agent.variables.placeholder':
    'Please select variables to store in the AI Agent',
  'new.intent.intention.rhetorical.question.not.collecting':
    'No rhetorical question collected for the current variable',
  'new.intent.intention.rhetorical.question.not.collecting.placeholder':
    'Please enter the utterance',
  'new.intent.add.variables': 'Add attribute',
  'new.intent.add.cancel.confirm':
    'Cancel will clear the form. Are you sure you want to cancel?',
  'new.intent.intention.llm.extract.attribute': 'LLM extract intent attribute',
  'new.intent.llm.extract.attribute.add.variables': 'Add Attribute',
  'new.intent.add.success.tips':
    'Congratulations! You have successfully created an intent. Next, you can create an AI Agent for this intent.',
  'new.intent.add.static.language.technique.tips':
    'You can create up to 100 intent speeches!',
  'new.intent.add.attribute.example.tips':
    'You can add up to four attribute examples for each variable!',
  'new.intent.add.verbal.variables.tips':
    'You can create up to 4 utterance variables!',
  'new.intent.intention.classification.management':
    'Intent category management',
  'new.intent.intention.classification.management.placeholder':
    'Please enter the intent category name, press Enter to search or click the button to add.',
  'new.intent.add.variables.only.tips':
    'Variables added must be unique and cannot be repeated!',
  'new.intent.add.variables.only.tips.1':
    'Please configure the variables included in the utterance for {variable}.',
  'new.intent.add.variables.only.tips.2':
    'The utterance is missing the {variable} variable. Please add the corresponding utterance.',
  'new.intent.add.variables.only.tips.3': 'Script {script} repetition!',
  'new.intent.add.ai.script.create':
    'The intent name and reason for intent request cannot be empty!',
  'new.intent.add.script.create.tips':
    'Note: Pressing enter will automatically generate variables.',
  'new.intent.intention.classification.tips':
    'Intent category cannot be empty!',
  'new.intent.intention.language.attribute.code': 'Attribute code',
  'new.intent.intention.language.attribute.format.requirement':
    'Attribute format requirement',
  'new.intent.intention.language.attribute.is.required': 'Is required',
  'new.intent.intention.language.attribute.operation': 'Operation',
  'new.intent.intention.language.attribute.is.required.yes': 'Yes',
  'new.intent.intention.language.attribute.is.required.no': 'No',
  'new.intent.intention.language.attribute.code.only.support.english':
    'Only English is supported',
  'new.intent.intention.language.attribute.code.only.support.english.message':
    'Attribute code only supports English',
  'new.intent.add.required.one.speech': 'Must have one script',
  'new.intent.add.dynamic.speech.editing.tips':
    'Please save the dynamic utterance intent attribute variable that is being edited',

  // 智能体列表
  'external.intelligent.agent.delete.synonym.rules': 'Delete prompt',
  'external.intelligent.agent.intent.language.placeholder':
    'Please select the intent name',
  'external.intelligent.agent.name': 'AI Agent name:: ',
  'external.intelligent.agent.name.placeholder':
    'Please enter the AI Agent name',
  'external.intelligent.agent.deployment.status': 'Status: ',
  'external.intelligent.agent.deployment.status.placeholder':
    'Please select deployment status',
  'external.intelligent.agent.tab.chat': 'Chat',
  'external.intelligent.agent.tab.email': 'Email',
  'external.intelligent.agent.tab.phone': 'Phone',
  'external.intelligent.agent.create.text': 'Create a new AI Agent',
  'external.intelligent.agent.use.rule': 'Usage rules: ',
  'external.intelligent.agent.deployment.time': 'Deployment time: ',
  'external.intelligent.agent.delete.tips':
    'Are you sure you want to delete this AI Agent?',
  'external.intelligent.agent.deploy.status.1': 'Deployed',
  'external.intelligent.agent.deploy.status.2': 'Draft',
  'external.intelligent.agent.default.welcome.message':
    'When customers communicate with enterprises via WEB online chat, APP online chat, Shopify, and WeChat Mini Program, the current welcome AI Agent will be executed first.',
  'external.intelligent.agent.fallback':
    "When the system doesn't match any AI Agent, it will execute the default AI Agent.",
  'intelligent.agent.deployed.text': 'Deployed, {deployName}',
  'external.intelligent.agent.share.success': 'Copy success',
  'external.intelligent.agent.share.tips':
    'Note: This share code is valid for 24 hours. Please use it before {expiryTime}.',
  'external.intelligent.agent.share': 'Share',
  'external.intelligent.agent.share.content':
    '【ConnectNow】You have received an AI Agent share from "{username}". Your exclusive experience code is: "{shareCode}". This code is valid for 24 hours (before {expiryTime}).',
  'external.intelligent.agent.share.confirm': 'Confirm',
  'external.intelligent.agent.share.copy': 'Copy',
  'external.intelligent.agent.share.cancel': 'Cancel',
  // 新建智能体
  'external.intelligent.agent.create.title': 'Create a new AI Agent',
  'external.intelligent.agent.create.add': 'Create a new AI Agent',
  'external.intelligent.agent.create.basic.information': 'Basic information',
  'external.intelligent.agent.create.name': 'Please enter AI Agent name',
  'external.intelligent.agent.create.channel.type': 'Applicable channel type',
  'external.intelligent.agent.create.select.scenario':
    'Select AI Agent scenario',
  'external.intelligent.agent.phone.voice': 'Phone/Voice',
  'external.intelligent.agent.create.select.channel.type':
    'Please select applicable channel type',
  'external.intelligent.agent.create.select.channel.type.tips':
    'At least one channel type must be selected!',
  'external.intelligent.agent.create.facebook.message': 'Facebook Message',
  'marketing.channel.type.web.voice': 'WEB Voice',
  'marketing.channel.type.app.voice': 'APP Voice',
};
