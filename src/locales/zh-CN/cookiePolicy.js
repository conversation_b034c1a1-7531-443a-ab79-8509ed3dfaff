import {
  documentUpdateAndEffectiveDate,
  documentEffectiveDate,
  companyAddress,
} from '@/utils/documentVar';
export default {
  'cookie.nav.title': 'Cookie政策',
  introduction: '引言',
  'cookie.p1':
    `本政策向您提供{companyName}（联系地址：${companyAddress.cn}，联系方式：************************以下简称“{companyName2}”或“我们”）运营的ConnectNow网站（网址: `,
  'cookie.p1.link': 'https://www.connectnowai.com',
  'cookie.p1.p1':
    '）使用的Cookie 和其他类似技术有关的信息。我们会不时审查本政策，以便确保其内容处于最新状态。',
  'cookie.p2':
    '如做出变更，我们会在此公布最新版本的政策。做出重大变更时，我们会在您下次访问我们的服务时或通过其他通信方式向您告知变更内容。',
  'cookie.section.one.prefix': '一、',
  'cookie.section.one': '我们可能使用哪些类似技术？',
  'cookie.section.two.prefix': '二、',
  'cookie.section.two': '第三方社交媒体情况',
  'cookie.section.three.prefix': '三、',
  'cookie.section.three': '如何管理 Cookie 设置？',
  'cookie.section.four.prefix': '四、',
  'cookie.section.four': '我们多久更新Cookie政策？',
  'cookie.section.five.prefix': '五、',
  'cookie.section.five': '在哪里可以得到进一步的信息？',

  'cookie.section.one.table.type': '技术类型',
  'cookie.section.one.table.description': '技术描述',
  'cookie.table.localStorage': 'Local storage',
  'cookie.table.localStorage.description':
    '本地存储可让网站或应用程序将信息本地存储在您的设备上。本地存储可用于改善ConnectNow网站体验，例如，启用功能、记住您的偏好以及加快网站功能。',
  'cookie.table.otherTechnologies': 'Other similar technologies',
  'cookie.table.otherTechnologies.description':
    '我们可能使用其他跟踪技术，本政策中Cookie类似技术的引用包括像素、本地存储和其他跟踪技术。',

  'cookie.section.two.p1':
    '除此之外，我们的网站上还可能涉及第三方社交媒体链接，具体如下：',
  'cookie.section.two.p2':
    '1.Facebook：Facebook 由 Meta 运营，其营业地址为 1 Hacker Way, Menlo Park, CA 94025, USA。点击此处查看 Facebook 的数据保护信息：',
  'cookie.section.two.p2.link':
    'https://www.facebook.com/privacy/policies/uso/',
  'cookie.section.two.p3':
    '2.Instagram：Instagram 由 Meta 运营，其营业地址为 1 Hacker Way, Menlo Park, CA 94025, USA。点击此处查看 Instagram 的数据保护信息：',
  'cookie.section.two.p3.link':
    'https://privacycenter.instagram.com/policy/?entry_point=ig_help_center_data_policy_redirect.',
  'cookie.section.two.p4':
    '3.Amazon：Amazon由Amazon Web Services EMEA SARL，其营业地址为：38 Avenue John F. Kennedy, L-1855, Luxembourg，点击此处查看Amazon的数据保护信息：',
  'cookie.section.two.p4.link': 'https://aws.amazon.com/privacy/?nc1=h_ls',

  'cookie.section.three.p1':
    '为了确保您在访问ConnectNow网站时得到最佳体验，我们建议您接受Cookie。但是，您可以通过点击“Cookie设置”按键来拒绝 Cookie的使用。',
  'cookie.section.three.p2':
    'Cookie设置中选项可以在ConnectNow网站上找到。如果您选择拒绝Cookie，您仍然可以使用我们的网站，同时网站的某些功能和区域可能受到限制。您还可以设置或修改Web浏览器设置以接受或拒绝Cookie。',
  'cookie.section.three.p3': '您可以通过以下链接获得有关下列浏览器的说明：',

  'cookie.section.three.p4': '•Internet Explorer - ',
  'cookie.section.three.p4.link':
    'http://windows.microsoft.com/en-gb/windows-vista/block-or-allow-cookies',
  'cookie.section.three.p5': '•Chrome - ',
  'cookie.section.three.p5.link':
    'https://support.google.com/chrome/answer/95647',
  'cookie.section.three.p6': '•Firefox - ',
  'cookie.section.three.p6.link':
    'https://support.mozilla.org/en-US/kb/enable-and-disable-cookies-website-preferences',
  'cookie.section.three.p7': '•Opera - ',
  'cookie.section.three.p7.link':
    'http://www.opera.com/help/tutorials/security/privacy/',
  'cookie.section.three.p8': '•Safari - ',
  'cookie.section.three.p8.link': 'http://support.apple.com/kb/PH17191',
  'cookie.section.three.p9': '•360安全浏览器 - ',
  'cookie.section.three.p9.link':
    'http://bbs.360.cn/forum.php?mod=viewthread&tid=14294828',
  'cookie.section.three.p10': '•搜狗浏览器 - ',
  'cookie.section.three.p10.link': 'http://ie.sogou.com/help2/help-4-4.html',
  'cookie.section.three.p11': '•QQ浏览器 - ',
  'cookie.section.three.p11.link': 'http://browser.qq.com/',
  'cookie.section.three.p12': '有关删除或阻止cookie的更多信息，请访问：',
  'cookie.section.three.p13': 'http://www.aboutcookies.org',

  'cookie.section.four.p1':
    '我们可能会不时或者出于其他运营、法律或监管原因更新此Cookie政策，以反映我们使用的Cookie的变化。因此，请定期重新阅读此Cookie政策，以了解我们对Cookie和相关技术的使用情况。此Cookie政策顶部的日期表示最近更新的时间。',

  'cookie.section.five.p1':
    '如果您对我们使用Cookie或其他技术有任何疑问，请通过以下方式与我们联系：',
  'cookie.section.five.p2': '客服电话：010- 53358729',
  'cookie.section.five.p3': '联系方式： <EMAIL>',
  'cookie.section.five.p4':
    '我们在收到您的问题、意见或建议，并在验证您的用户身份后，我们将在适用的法律规定的期限内回复（如中国境内为15日内，欧盟地区为30日）。',

  'cookie.updateDate': documentUpdateAndEffectiveDate.cn,
  'cookie.effectiveDate': documentEffectiveDate.cn,
};
